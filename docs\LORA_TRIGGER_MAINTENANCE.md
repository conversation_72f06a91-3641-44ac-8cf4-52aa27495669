# LoRA触发词维护指南

## 概述

为了提高LoRA模型匹配的准确性，我们移除了不可靠的文件名和模型名匹配，改为完全依赖触发词匹配。这需要人工维护高质量的触发词。

## 问题背景

### 为什么移除文件名匹配？

LoRA模型作者经常使用创意命名，导致匹配失败：

```
❌ 失败案例：
- "artchitecture" → 用户搜索 "architecture" 匹配不到
- "CyberNinja" → 用户搜索 "cyberpunk" 或 "ninja" 都匹配不到  
- "DreamScape" → 用户搜索 "landscape" 匹配不到
- "UltraRealism" → 用户搜索 "realistic" 匹配不到
```

### 触发词的优势

- ✅ **作者精心设置** - 直接描述模型功能
- ✅ **多语言支持** - 同时包含中英文
- ✅ **功能导向** - 描述模型能做什么，不是"酷炫"名字

## 配置文件保护机制

### 自动保护人工修改

系统会自动保护人工维护的模型：

```json
{
  "name": "artchitecture_Flux1",
  "trigger_words": ["建筑", "艺术", "architecture", "设计"],
  "manually_maintained": true,  // 🔒 保护标记
  "last_manual_update": "2025-01-17"
}
```

### 保护规则

1. **人工维护模型** (`manually_maintained: true`)
   - ✅ 只更新安全字段：`file_path`, `civitai_id`, `rating`, `downloads`
   - ❌ 不更新：`trigger_words`, `category`, `description`

2. **自动维护模型** (`manually_maintained: false`)
   - ✅ 可以更新所有字段
   - ⚠️ 触发词和分类只在原来为空时更新

## 保护机制详解

### 三种保护触发方式

1. **使用维护工具编辑** ✅ 自动保护
   ```bash
   python maintain_lora_triggers.py maintain "artchitecture_Flux1"
   # 编辑完成后自动设置 manually_maintained: true
   ```

2. **直接编辑JSON文件后手动标记**
   ```bash
   # 编辑 config/lora_models.json 后
   python maintain_lora_triggers.py protect "artchitecture_Flux1"
   ```

3. **自动检测人工修改**
   ```bash
   python maintain_lora_triggers.py auto-detect
   # 自动检测并标记所有人工修改的模型
   ```

### 自动检测规则

系统会自动检测以下特征来判断是否为人工修改：
- ✅ 触发词数量 ≥ 3个
- ✅ 包含中文触发词
- ✅ 描述不是"自动发现的Lora模型..."
- ✅ 分类不是默认的"other"

## 维护工具使用

### 1. 查看需要维护的模型

```bash
python maintain_lora_triggers.py list
```

输出示例：
```
📋 发现 15 个需要维护的模型:

 1. flux1-canny-dev-lora
     📂 flux1-canny-dev-lora.safetensors
     🏷️  other
     🎯 触发词: []
     📝 自动发现的Lora模型: flux1-canny-dev-lora...

 2. outfit-generator
     📂 outfit-generator.safetensors
     🏷️  other
     🎯 触发词: ["generator", "outfit"]
     📝 自动发现的Lora模型: outfit-generator...
```

### 2. 维护单个模型

```bash
python maintain_lora_triggers.py maintain "artchitecture_Flux1"
```

交互式编辑：
```
🔧 维护模型: artchitecture_Flux1
📂 文件: artchitecture_Flux1.safetensors
🏷️ 当前分类: other
🎯 当前触发词: []

可用分类:
  1. architecture
  2. anime
  3. detail
  4. style
  5. portrait
  6. landscape
  7. other

选择分类 (1-7, 回车保持当前): 1
✅ 分类更新为: architecture

🎯 编辑触发词:
提示：用逗号分隔多个触发词，支持中英文
建议：包含功能描述、风格特征、适用场景等

触发词 (当前: ): 建筑,艺术,architecture,设计,渲染
✅ 触发词更新为: ['建筑', '艺术', 'architecture', '设计', '渲染']

📝 编辑描述:
描述 (当前: 自动发现的Lora模型...): Flux1建筑艺术风格，适合建筑设计和艺术渲染
✅ 描述已更新

✅ 模型 artchitecture_Flux1 维护完成并已保存
```

### 3. 批量维护

```bash
python maintain_lora_triggers.py batch architecture
```

使用预设模板：
```
📋 找到 5 个 'architecture' 分类的模型
💡 建议的触发词模板: ['建筑', 'architecture', '设计', '渲染']
是否使用模板触发词？(y/n): y
✅ 已更新: ASTRA_Flux_OC_Vbeta-2
✅ 已更新: 黑格建筑效果表现
✅ 已更新: artchitecture_Flux1
✅ 批量维护完成，共更新 5 个模型
```

### 4. 验证触发词质量

```bash
python maintain_lora_triggers.py validate
```

质量检查：
```
🔍 验证触发词质量...
⚠️ 发现 3 个模型存在问题:
  📛 flux1-canny-dev-lora
     - 触发词数量不足（建议至少2个）
     - 缺少中文触发词
     - 未正确分类
  📛 pytorch_lora_weights
     - 缺少中文触发词
     - 未正确分类
```

### 5. 手动标记保护状态

```bash
python maintain_lora_triggers.py protect "artchitecture_Flux1"
```

适用场景：
- 直接编辑了JSON文件
- 通过其他脚本修改了模型信息
- 需要手动标记某个模型为保护状态

### 6. 自动检测人工修改

```bash
python maintain_lora_triggers.py auto-detect
```

输出示例：
```
🔍 自动检测到人工修改: artchitecture_Flux1
🔍 自动检测到人工修改: ASTRA_Flux_OC_Vbeta-2
✅ 自动标记了 2 个人工修改的模型
```

### 7. 创建备份

```bash
python maintain_lora_triggers.py backup
```

## 触发词设置建议

### 优质触发词特征

1. **功能描述** - 直接说明模型能做什么
2. **中英文并存** - 支持不同语言用户
3. **多层次覆盖** - 从具体到抽象
4. **避免技术术语** - 使用用户常用词汇

### 分类模板

```json
{
  "architecture": ["建筑", "architecture", "设计", "渲染"],
  "anime": ["动漫", "anime", "二次元", "卡通"],
  "detail": ["细节", "detail", "增强", "高清"],
  "style": ["风格", "style", "艺术", "效果"],
  "portrait": ["人像", "portrait", "肖像", "人物"],
  "landscape": ["风景", "landscape", "自然", "景观"]
}
```

### 实际案例

```json
// ❌ 差的触发词
{
  "name": "artchitecture_Flux1",
  "trigger_words": ["artchitecture", "flux1"]
}

// ✅ 好的触发词
{
  "name": "artchitecture_Flux1", 
  "trigger_words": ["建筑", "艺术", "architecture", "设计", "渲染", "建筑风格"],
  "manually_maintained": true
}
```

## 工作流程

### 日常维护流程

1. **定期检查** - 每周运行 `validate` 检查质量
2. **新模型维护** - Civitai下载后及时维护触发词
3. **用户反馈** - 根据匹配效果调整触发词
4. **备份保护** - 重要修改前创建备份

### 批量维护流程

1. **创建备份** - `python maintain_lora_triggers.py backup`
2. **查看候选** - `python maintain_lora_triggers.py list`
3. **分类维护** - `python maintain_lora_triggers.py batch <分类>`
4. **质量验证** - `python maintain_lora_triggers.py validate`
5. **测试效果** - 实际使用测试匹配效果

## 注意事项

### ⚠️ 重要提醒

1. **备份优先** - 重要修改前务必备份
2. **测试验证** - 修改后测试匹配效果
3. **渐进改进** - 不要一次性修改太多模型
4. **用户导向** - 以用户常用词汇为准

### 🔒 安全保护

- 人工维护的模型会被自动保护
- `discover_lora_models.py` 不会覆盖人工修改
- 配置文件有备份机制
- 支持回滚操作

## 效果对比

### 修改前（文件名匹配）
```
用户输入: "建筑风格的房子"
❌ artchitecture_Flux1 - 匹配失败（architecture ≠ artchitecture）
```

### 修改后（触发词匹配）
```
用户输入: "建筑风格的房子"  
✅ artchitecture_Flux1 - 匹配成功（触发词包含"建筑"）
```

通过精心维护触发词，可以显著提高LoRA模型匹配的准确性和用户体验。
