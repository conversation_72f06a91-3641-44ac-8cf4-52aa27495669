#!/usr/bin/env python3
"""
LoRA触发词人工维护工具

功能：
1. 列出需要维护的模型
2. 批量编辑触发词
3. 标记为人工维护状态
4. 验证触发词质量

作者：二次开发标识
"""

import json
import sys
import os
from pathlib import Path
from typing import List, Dict, Optional

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from pkg.workers.shared.shared_lora_manager import LoraCategory


class LoRATriggerMaintainer:
    """LoRA触发词维护工具"""
    
    def __init__(self):
        self.config_file = "config/lora_models.json"
        self.backup_file = "config/lora_models_backup.json"
    
    def create_backup(self):
        """创建配置文件备份"""
        if os.path.exists(self.config_file):
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            with open(self.backup_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 已创建备份: {self.backup_file}")
    
    def load_config(self) -> Dict:
        """加载配置文件"""
        with open(self.config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def save_config(self, config: Dict):
        """保存配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    
    def list_models_needing_maintenance(self) -> List[Dict]:
        """列出需要维护的模型"""
        config = self.load_config()
        models_needing_maintenance = []
        
        for model in config["models"]:
            # 检查是否需要维护
            needs_maintenance = (
                not model.get("manually_maintained", False) and (
                    not model.get("trigger_words") or  # 没有触发词
                    len(model.get("trigger_words", [])) < 2 or  # 触发词太少
                    model.get("description", "").startswith("自动发现的Lora模型") or  # 自动生成的描述
                    model.get("category") == "other"  # 未分类
                )
            )
            
            if needs_maintenance:
                models_needing_maintenance.append(model)
        
        return models_needing_maintenance
    
    def show_maintenance_candidates(self):
        """显示需要维护的候选模型"""
        models = self.list_models_needing_maintenance()
        
        if not models:
            print("🎉 所有模型都已维护完成！")
            return
        
        print(f"📋 发现 {len(models)} 个需要维护的模型:")
        print()
        
        for i, model in enumerate(models, 1):
            print(f"{i:2d}. {model['name']}")
            print(f"     📂 {model['filename']}")
            print(f"     🏷️  {model['category']}")
            print(f"     🎯 触发词: {model.get('trigger_words', [])}")
            print(f"     📝 {model.get('description', '')[:50]}...")
            print()
    
    def maintain_model(self, model_name: str):
        """维护单个模型"""
        config = self.load_config()
        
        # 找到模型
        model = None
        for m in config["models"]:
            if m["name"] == model_name:
                model = m
                break
        
        if not model:
            print(f"❌ 模型不存在: {model_name}")
            return
        
        print(f"🔧 维护模型: {model['name']}")
        print(f"📂 文件: {model['filename']}")
        print(f"🏷️ 当前分类: {model['category']}")
        print(f"🎯 当前触发词: {model.get('trigger_words', [])}")
        print(f"📝 当前描述: {model.get('description', '')}")
        print()
        
        # 编辑分类
        print("可用分类:")
        categories = [cat.value for cat in LoraCategory]
        for i, cat in enumerate(categories, 1):
            print(f"  {i}. {cat}")
        
        try:
            cat_choice = input(f"选择分类 (1-{len(categories)}, 回车保持当前): ").strip()
            if cat_choice:
                new_category = categories[int(cat_choice) - 1]
                model["category"] = new_category
                print(f"✅ 分类更新为: {new_category}")
        except (ValueError, IndexError):
            print("⚠️ 无效选择，保持当前分类")
        
        # 编辑触发词
        print("\n🎯 编辑触发词:")
        print("提示：用逗号分隔多个触发词，支持中英文")
        print("建议：包含功能描述、风格特征、适用场景等")
        
        current_triggers = ", ".join(model.get("trigger_words", []))
        new_triggers = input(f"触发词 (当前: {current_triggers}): ").strip()
        
        if new_triggers:
            # 解析触发词
            trigger_list = [t.strip() for t in new_triggers.split(",") if t.strip()]
            model["trigger_words"] = trigger_list
            print(f"✅ 触发词更新为: {trigger_list}")
        
        # 编辑描述
        print("\n📝 编辑描述:")
        current_desc = model.get("description", "")
        new_desc = input(f"描述 (当前: {current_desc[:50]}...): ").strip()
        
        if new_desc:
            model["description"] = new_desc
            print(f"✅ 描述已更新")
        
        # 标记为人工维护
        model["manually_maintained"] = True
        model["last_manual_update"] = "2025-01-17"  # 记录维护时间
        
        # 保存配置
        self.save_config(config)
        print(f"✅ 模型 {model_name} 维护完成并已保存")
    
    def batch_maintain_by_category(self, category: str):
        """按分类批量维护"""
        config = self.load_config()
        models_in_category = [m for m in config["models"] if m["category"] == category]
        
        if not models_in_category:
            print(f"❌ 没有找到分类为 '{category}' 的模型")
            return
        
        print(f"📋 找到 {len(models_in_category)} 个 '{category}' 分类的模型")
        
        # 批量设置触发词模板
        templates = {
            "architecture": ["建筑", "architecture", "设计", "渲染"],
            "anime": ["动漫", "anime", "二次元", "卡通"],
            "detail": ["细节", "detail", "增强", "高清"],
            "style": ["风格", "style", "艺术", "效果"],
            "portrait": ["人像", "portrait", "肖像", "人物"],
            "landscape": ["风景", "landscape", "自然", "景观"]
        }
        
        template_triggers = templates.get(category, [])
        if template_triggers:
            print(f"💡 建议的触发词模板: {template_triggers}")
            use_template = input("是否使用模板触发词？(y/n): ").strip().lower()
            
            if use_template == 'y':
                for model in models_in_category:
                    if not model.get("manually_maintained", False):
                        model["trigger_words"] = template_triggers.copy()
                        model["manually_maintained"] = True
                        model["last_manual_update"] = "2025-01-17"
                        print(f"✅ 已更新: {model['name']}")
                
                self.save_config(config)
                print(f"✅ 批量维护完成，共更新 {len(models_in_category)} 个模型")
    
    def validate_trigger_quality(self):
        """验证触发词质量"""
        config = self.load_config()
        
        print("🔍 验证触发词质量...")
        
        issues = []
        for model in config["models"]:
            model_issues = []
            
            # 检查触发词数量
            triggers = model.get("trigger_words", [])
            if len(triggers) < 2:
                model_issues.append("触发词数量不足（建议至少2个）")
            
            # 检查是否包含中文触发词
            has_chinese = any('\u4e00' <= char <= '\u9fff' for trigger in triggers for char in trigger)
            if not has_chinese:
                model_issues.append("缺少中文触发词")
            
            # 检查是否包含英文触发词
            has_english = any(trigger.isascii() and trigger.isalpha() for trigger in triggers)
            if not has_english:
                model_issues.append("缺少英文触发词")
            
            # 检查分类是否合理
            if model.get("category") == "other":
                model_issues.append("未正确分类")
            
            if model_issues:
                issues.append({
                    "name": model["name"],
                    "issues": model_issues
                })
        
        if issues:
            print(f"⚠️ 发现 {len(issues)} 个模型存在问题:")
            for issue in issues:
                print(f"  📛 {issue['name']}")
                for problem in issue['issues']:
                    print(f"     - {problem}")
        else:
            print("✅ 所有模型的触发词质量都很好！")


def main():
    """主函数"""
    maintainer = LoRATriggerMaintainer()
    
    if len(sys.argv) < 2:
        print("LoRA触发词维护工具")
        print()
        print("用法:")
        print("  python maintain_lora_triggers.py list              # 列出需要维护的模型")
        print("  python maintain_lora_triggers.py maintain <模型名>  # 维护指定模型")
        print("  python maintain_lora_triggers.py batch <分类>       # 批量维护指定分类")
        print("  python maintain_lora_triggers.py validate          # 验证触发词质量")
        print("  python maintain_lora_triggers.py backup            # 创建备份")
        return
    
    command = sys.argv[1]
    
    try:
        if command == "list":
            maintainer.show_maintenance_candidates()
        
        elif command == "maintain":
            if len(sys.argv) < 3:
                print("❌ 请指定模型名称")
                return
            model_name = sys.argv[2]
            maintainer.create_backup()
            maintainer.maintain_model(model_name)
        
        elif command == "batch":
            if len(sys.argv) < 3:
                print("❌ 请指定分类名称")
                return
            category = sys.argv[2]
            maintainer.create_backup()
            maintainer.batch_maintain_by_category(category)
        
        elif command == "validate":
            maintainer.validate_trigger_quality()
        
        elif command == "backup":
            maintainer.create_backup()
        
        else:
            print(f"❌ 未知命令: {command}")
    
    except Exception as e:
        print(f"❌ 执行失败: {e}")


if __name__ == "__main__":
    main()
