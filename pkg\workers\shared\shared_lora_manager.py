"""
[二次开发] 共享 LoRA 管理器

提供跨工作流的 LoRA 模型管理功能

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：所有工作流共享的LoRA模型管理
- 维护者：开发团队
- 最后更新：2024-12-20
- 相关任务：DEV-05 配置模块实现
- 依赖关系：依赖本地JSON/YAML配置文件
"""

import json
import logging
import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum


class LoraCategory(Enum):
    """Lora模型分类"""
    ARCHITECTURE = "architecture"  # 建筑类
    PORTRAIT = "portrait"          # 人像类
    LANDSCAPE = "landscape"        # 风景类
    ANIME = "anime"                # 动漫类
    DETAIL = "detail"              # 细节增强类
    STYLE = "style"                # 风格类
    OBJECT = "object"              # 物体类
    OTHER = "other"                # 其他类

    @classmethod
    def get_priority_score(cls, category) -> int:
        """获取分类的优先级分数（分数越高优先级越高）"""
        # 风格类LoRA - 高优先级
        style_categories = {
            cls.ANIME: 100,      # 动漫风格
            cls.STYLE: 95,       # 通用风格
            cls.DETAIL: 90,      # 细节增强
        }

        # 内容类LoRA - 低优先级
        content_categories = {
            cls.LANDSCAPE: 60,   # 风景
            cls.PORTRAIT: 50,    # 人像
            cls.ARCHITECTURE: 40, # 建筑
            cls.OBJECT: 30,      # 物体
            cls.OTHER: 20,       # 其他
        }

        return style_categories.get(category, content_categories.get(category, 10))


@dataclass
class LoraModel:
    """Lora模型配置"""
    name: str                    # 模型名称
    filename: str                # 文件名
    file_path: str               # 文件路径
    category: LoraCategory       # 分类
    model_type: str              # 模型类型 (flux/sdxl/sd15)
    weight: float                # 默认权重
    trigger_words: List[str]     # 触发词
    description: str             # 描述
    civitai_id: Optional[str]    # Civitai ID
    civitai_url: Optional[str]   # Civitai URL
    rating: Optional[float]      # 评分
    downloads: Optional[int]     # 下载次数
    is_local: bool               # 是否为本地模型
    is_active: bool              # 是否启用
    is_priority: bool            # 是否为优先模型（通用性强）


class SharedLoraManager:
    """共享 LoRA 管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.lora_models: Dict[str, LoraModel] = {}
        self._initialized = False
        
    def initialize(self):
        """初始化管理器，加载配置文件中的模型"""
        if self._initialized:
            return

        try:
            # 从JSON配置文件加载（唯一配置源）
            self._load_from_json_config()
            self.logger.info(f"从JSON配置文件加载了 {len(self.lora_models)} 个LoRA模型")
        except Exception as e:
            self.logger.error(f"从JSON配置文件加载失败: {e}")
            # 加载默认模型作为备用
            self._load_default_models()
            self.logger.info("加载了默认LoRA模型")

        self._initialized = True
    
    def _load_from_json_config(self):
        """从JSON配置文件加载模型"""
        config_path = "config/lora_models.json"
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        models_data = config_data.get('models', [])
        for model_data in models_data:
            try:
                # 映射分类字符串到枚举
                category_str = model_data.get('category', 'other')
                category = self._map_category_string(category_str)
                
                model = LoraModel(
                    name=model_data['name'],
                    filename=model_data['filename'],
                    file_path=model_data.get('file_path', ''),
                    category=category,
                    model_type=model_data.get('model_type', 'flux'),  # 默认为flux
                    weight=model_data.get('weight', 0.8),
                    trigger_words=model_data.get('trigger_words', []),
                    description=model_data.get('description', ''),
                    civitai_id=model_data.get('civitai_id'),
                    civitai_url=model_data.get('civitai_url'),
                    rating=model_data.get('rating'),
                    downloads=model_data.get('downloads'),
                    is_local=model_data.get('is_local', True),
                    is_active=model_data.get('is_active', True),
                    is_priority=model_data.get('is_priority', False)
                )
                
                self.lora_models[model.name] = model
                
            except Exception as e:
                self.logger.warning(f"加载模型 {model_data.get('name', 'unknown')} 失败: {e}")
    
    # YAML配置文件支持已移除
    # 现在只使用JSON配置文件作为唯一配置源
    # 原YAML配置已迁移到JSON文件中
    
    def _load_default_models(self):
        """加载默认模型"""
        default_models = [
            LoraModel(
                name="detail_aidmafluxproultra-FLUX-v0.1",
                filename="detail_aidmafluxproultra-FLUX-v0.1.safetensors",
                file_path="/home/<USER>/Workspace/ComfyUI/models/loras/detail_aidmafluxproultra-FLUX-v0.1.safetensors",
                category=LoraCategory.DETAIL,
                weight=0.8,
                trigger_words=["细节", "detail", "高清", "high quality"],
                description="FLUX细节增强模型",
                civitai_id=None,
                civitai_url=None,
                rating=None,
                downloads=None,
                is_local=True,
                is_active=True,
                is_priority=True
            ),
            LoraModel(
                name="Anime_niji",
                filename="Anime_niji.safetensors",
                file_path="/home/<USER>/Workspace/ComfyUI/models/loras/Anime_niji.safetensors",
                category=LoraCategory.ANIME,
                model_type="flux",
                weight=0.8,
                trigger_words=["动漫", "anime", "二次元", "manga"],
                description="Niji动漫风格",
                civitai_id=None,
                civitai_url=None,
                rating=None,
                downloads=None,
                is_local=True,
                is_active=True,
                is_priority=False
            ),
            LoraModel(
                name="Flux Flat Anime",
                filename="Flux Flat Anime.safetensors",
                file_path="/home/<USER>/Workspace/ComfyUI/models/loras/Flux Flat Anime.safetensors",
                category=LoraCategory.ANIME,
                model_type="flux",
                weight=0.8,
                trigger_words=["动漫", "anime", "二次元", "manga"],
                description="Flux扁平化动漫风格",
                civitai_id=None,
                civitai_url=None,
                rating=None,
                downloads=None,
                is_local=True,
                is_active=True,
                is_priority=False
            )
        ]
        
        for model in default_models:
            self.lora_models[model.name] = model
    
    def _map_category_string(self, category_str: str) -> LoraCategory:
        """映射分类字符串到枚举"""
        category_mapping = {
            "architecture": LoraCategory.ARCHITECTURE,
            "portrait": LoraCategory.PORTRAIT,
            "landscape": LoraCategory.LANDSCAPE,
            "anime": LoraCategory.ANIME,
            "detail": LoraCategory.DETAIL,
            "style": LoraCategory.STYLE,
            "object": LoraCategory.OBJECT,
            "other": LoraCategory.OTHER
        }
        return category_mapping.get(category_str.lower(), LoraCategory.OTHER)

    def _tokenize_input(self, text: str) -> List[str]:
        """将文本分词为小写关键词列表"""
        import re
        # 移除标点符号，转换为小写，然后分割
        cleaned_text = re.sub(r'[^\w\s]', '', text).lower()
        return [word for word in cleaned_text.split() if word]
        
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        # 确保已初始化
        if not self._initialized:
            self.initialize()
            
        total = len(self.lora_models)
        active = sum(1 for model in self.lora_models.values() if model.is_active)
        local = sum(1 for model in self.lora_models.values() if model.is_local)
        remote = total - local
        priority = sum(1 for model in self.lora_models.values() if model.is_priority)
        
        # 按分类统计
        by_category = {}
        for model in self.lora_models.values():
            category = model.category.value
            if category not in by_category:
                by_category[category] = 0
            by_category[category] += 1
        
        return {
            "total": total,
            "active": active,
            "local": local,
            "remote": remote,
            "priority": priority,
            "by_category": by_category
        }
    
    def get_all_models(self) -> List[LoraModel]:
        """获取所有模型"""
        # 确保已初始化
        if not self._initialized:
            self.initialize()
            
        return list(self.lora_models.values())
    
    def get_models_by_category(self, category: LoraCategory) -> List[LoraModel]:
        """根据分类获取模型"""
        # 确保已初始化
        if not self._initialized:
            self.initialize()
            
        return [model for model in self.lora_models.values() if model.category == category]
    
    def get_models_by_trigger(self, search_keywords: List[str], model_type: str = "flux") -> List[LoraModel]:
        """根据触发词获取模型（智能匹配版本）"""
        # 确保已初始化
        if not self._initialized:
            self.initialize()

        matching_models = []
        # 将输入的关键词列表转换为小写，并确保唯一性
        processed_keywords = [kw.lower() for kw in search_keywords]
        # 移除重复关键词
        processed_keywords = list(set(processed_keywords))

        # 定义近义词映射
        synonym_mapping = {
            "建筑": ["architecture", "building", "house", "construction", "建筑", "房屋", "楼房"],
            "人像": ["portrait", "person", "face", "character", "人像", "人物", "角色", "头像"],
            "风景": ["landscape", "nature", "scenery", "风景", "自然", "景观", "山水"],
            "动漫": ["anime", "manga", "cartoon", "动漫", "漫画", "卡通", "二次元"],
            "细节": ["detail", "enhance", "quality", "upscale", "细节", "增强", "质量", "高清"],
            "写实": ["realistic", "photorealistic", "真实", "写实", "照片"],
            "风格": ["style", "art", "artistic", "风格", "艺术", "画风"]
        }

        if not processed_keywords:
            self.logger.info("没有有效的搜索关键词，返回空列表")
            return []
        
        for model in self.lora_models.values():
            if not model.is_active:
                continue

            # 过滤模型类型（只匹配对应类型的模型）
            if model.model_type != model_type:
                continue

            current_score = 0.0
            
            
            
            # 1. 检查触发词智能匹配 (累积加分) - 主要匹配方式
            for trigger_word in model.trigger_words:
                trigger_lower = trigger_word.lower()

                # 精确匹配 - 提高权重，因为这是最可靠的匹配
                if trigger_lower in processed_keywords:
                    current_score += 10.0  # 从5.0提高到10.0

                # 近义词匹配 - 提高权重
                for category, synonyms in synonym_mapping.items():
                    if trigger_lower in synonyms:
                        for synonym in synonyms:
                            if synonym in processed_keywords:
                                current_score += 6.0  # 从3.0提高到6.0
                                break # 找到一个近义词匹配即可

            # 2. 移除不可靠的文件名和模型名匹配
            # 原因：创意命名（如artchitecture）会导致匹配失败
            # 只依赖精心设置的触发词进行匹配

            # 注释掉的不可靠匹配方式：
            # model_name_lower = model.name.lower()
            # model_filename_lower = model.filename.lower()
            # for keyword in processed_keywords:
            #     if keyword in model_name_lower:
            #         current_score += 4.0 # 名称包含关键词 - 不可靠
            #     if keyword in model_filename_lower:
            #         current_score += 4.0 # 文件名包含关键词 - 不可靠

            # 3. 移除描述匹配，因为它引入了太多噪声
            # if user_input_lower in model.description.lower():
            #     match_score += 2.0
            
            # 4. 分类优先级加分（风格类LoRA优先级更高）
            category_score = LoraCategory.get_priority_score(model.category) / 100.0
            current_score += category_score

            # 5. 优先级模型加分
            if model.is_priority:
                current_score += 1.0

            # 6. 评分加分
            if model.rating:
                current_score += model.rating / 5.0

            if current_score > 0:
                matching_models.append((model, current_score))
        
        # 按匹配分数排序
        matching_models.sort(key=lambda x: x[1], reverse=True)
        
        # 设置最低分数阈值，过滤掉不相关的模型
        # 由于提高了触发词匹配权重，相应提高阈值
        MIN_SCORE_THRESHOLD = 5.0 # 从2.0提高到5.0，确保只匹配真正相关的模型
        filtered_models = [(model, score) for model, score in matching_models if score >= MIN_SCORE_THRESHOLD]

        # 按匹配分数排序
        filtered_models.sort(key=lambda x: x[1], reverse=True)
        
        # 返回模型列表（不包含分数）
        return [model for model, score in filtered_models]
    
    async def update_from_civitai(self, api_key: Optional[str] = None, query: str = "flux", limit: int = 50):
        """从Civitai更新模型信息（真实实现）"""
        try:
            from .civitai_client import civitai_client

            self.logger.info(f"从Civitai搜索模型: {query} (限制: {limit})")

            # 搜索模型
            models = await civitai_client.search_models(
                query=query,
                types="LORA",
                sort="Highest Rated",
                limit=limit,
                nsfw=False
            )

            if not models:
                return f"未找到匹配 '{query}' 的模型"

            # 更新本地模型信息
            updated_count = 0
            for civitai_model in models:
                try:
                    # 创建或更新LoRA模型
                    model_name = f"civitai_{civitai_model.id}_{civitai_model.name}"

                    # 检查是否已存在
                    if model_name in self.lora_models:
                        # 更新现有模型的Civitai信息
                        existing_model = self.lora_models[model_name]
                        existing_model.civitai_id = str(civitai_model.id)
                        existing_model.civitai_url = f"https://civitai.com/models/{civitai_model.id}"
                        existing_model.rating = civitai_model.rating
                        existing_model.downloads = civitai_model.download_count
                        existing_model.description = civitai_model.description[:200]  # 限制长度
                        existing_model.trigger_words = civitai_model.tags[:10]  # 限制标签数量
                    else:
                        # 创建新的远程模型记录
                        new_model = LoraModel(
                            name=model_name,
                            filename=f"{civitai_model.name}.safetensors",  # 假设文件名
                            file_path="",  # 远程模型暂无本地路径
                            category=self._guess_category_from_tags(civitai_model.tags),
                            model_type="flux",  # Civitai搜索默认为flux类型
                            weight=0.8,
                            trigger_words=civitai_model.tags[:10],
                            description=civitai_model.description[:200],
                            civitai_id=str(civitai_model.id),
                            civitai_url=f"https://civitai.com/models/{civitai_model.id}",
                            rating=civitai_model.rating,
                            downloads=civitai_model.download_count,
                            is_local=False,  # 远程模型
                            is_active=False,  # 默认不激活远程模型
                            is_priority=False
                        )
                        self.lora_models[model_name] = new_model

                    updated_count += 1

                except Exception as e:
                    self.logger.warning(f"更新模型 {civitai_model.name} 失败: {e}")

            self.logger.info(f"成功更新 {updated_count} 个模型信息")
            return f"✅ 已从Civitai更新 {updated_count} 个模型信息"

        except Exception as e:
            self.logger.error(f"从Civitai更新模型失败: {e}")
            return f"❌ 更新失败: {str(e)}"
    
    def update_model(self, name: str, **kwargs):
        """更新模型配置"""
        if name not in self.lora_models:
            raise ValueError(f"模型不存在: {name}")
        
        model = self.lora_models[name]
        for key, value in kwargs.items():
            if hasattr(model, key):
                setattr(model, key, value)
        
        self.logger.info(f"更新模型配置: {name} - {kwargs}")
        return f"模型 {name} 配置已更新"

    def _guess_category_from_tags(self, tags: List[str]) -> LoraCategory:
        """根据标签猜测模型分类"""
        tags_lower = [tag.lower() for tag in tags]

        # 建筑类关键词
        if any(keyword in tags_lower for keyword in ['architecture', 'building', 'house', 'construction', '建筑', '房屋']):
            return LoraCategory.ARCHITECTURE

        # 人像类关键词
        if any(keyword in tags_lower for keyword in ['portrait', 'person', 'face', 'character', '人像', '人物', '角色']):
            return LoraCategory.PORTRAIT

        # 风景类关键词
        if any(keyword in tags_lower for keyword in ['landscape', 'nature', 'scenery', '风景', '自然', '景观']):
            return LoraCategory.LANDSCAPE

        # 动漫类关键词
        if any(keyword in tags_lower for keyword in ['anime', 'manga', 'cartoon', '动漫', '漫画', '卡通']):
            return LoraCategory.ANIME

        # 细节增强类关键词
        if any(keyword in tags_lower for keyword in ['detail', 'enhance', 'quality', 'upscale', '细节', '增强', '质量']):
            return LoraCategory.DETAIL

        # 风格类关键词
        if any(keyword in tags_lower for keyword in ['style', 'art', 'artistic', '风格', '艺术']):
            return LoraCategory.STYLE

        # 物体类关键词
        if any(keyword in tags_lower for keyword in ['object', 'item', 'tool', '物体', '物品', '工具']):
            return LoraCategory.OBJECT

        # 默认为其他类
        return LoraCategory.OTHER

    async def download_civitai_model(self, model_name: str) -> Optional[str]:
        """下载Civitai模型到本地"""
        try:
            if model_name not in self.lora_models:
                return None

            model = self.lora_models[model_name]
            if model.is_local or not model.civitai_id:
                return None

            from .civitai_client import civitai_client

            # 获取模型详情
            civitai_model = await civitai_client.get_model_details(int(model.civitai_id))
            if not civitai_model:
                return None

            # 下载模型
            local_path = await civitai_client.download_model(civitai_model)
            if local_path:
                # 更新模型信息
                model.file_path = local_path
                model.is_local = True
                model.filename = os.path.basename(local_path)

                self.logger.info(f"成功下载模型: {model_name} -> {local_path}")
                return local_path

            return None

        except Exception as e:
            self.logger.error(f"下载Civitai模型失败: {e}")
            return None

    def check_lora_status(self, model_name: str) -> Dict[str, Any]:
        """检查LoRA模型状态"""
        if model_name not in self.lora_models:
            return {"error": f"模型不存在: {model_name}"}

        model = self.lora_models[model_name]

        status = {
            "name": model.name,
            "category": model.category.value,
            "is_local": model.is_local,
            "is_active": model.is_active,
            "weight": model.weight,
            "file_path": model.file_path,
            "civitai_id": model.civitai_id,
            "civitai_url": model.civitai_url,
            "rating": model.rating,
            "downloads": model.downloads,
            "trigger_words": model.trigger_words,
            "description": model.description
        }

        # 检查本地文件状态
        if model.is_local and model.file_path:
            from .civitai_client import civitai_client
            file_status = civitai_client.check_model_status(os.path.basename(model.file_path))
            status.update({
                "file_exists": file_status["exists"],
                "file_size": file_status["size"],
                "file_readable": file_status["readable"]
            })

        return status


# 全局实例
_lora_manager = None


def get_lora_manager() -> SharedLoraManager:
    """获取全局 LoRA 管理器实例"""
    global _lora_manager
    if _lora_manager is None:
        _lora_manager = SharedLoraManager()
        _lora_manager.initialize()  # 自动初始化
    return _lora_manager 